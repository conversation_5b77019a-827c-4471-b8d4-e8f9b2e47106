import { Content } from '@/pkgs/content/types'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack, Typography } from '@mui/material'
import { ReservationSwitch } from '@/pkgs/reservation/ReservationSwitch'
import { useState } from 'react'
import { httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { notify } from '@/helpers'

type PublishToLiveProps<T extends Content> = {
    value: T
    onPublish: () => void
}

export function PublishToLive<T extends Content>({ value, onPublish }: PublishToLiveProps<T>) {
    const [liveIsAvailable, setLiveIsAvailable] = useState(false)
    const [isPublishing, setIsPublishing] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // /api/v2/content/:id/:workspace
    const publish = () => {
        setIsPublishing(true)
        httpPut(`${BASE}/api/v2/content/${value.ID}/live`, null)
            .then(() => {
                notify('Successfully published to live', 'info')
                onPublish()
            })
            .catch((err) => {
                setError(guessErrorMessage(err))
            })
            .finally(() => setIsPublishing(false))
    }

    return (
        <Stack direction='column' spacing={2}>
            <ReservationSwitch
                ID={value.ID}
                Workspace={'live'}
                table={'content'}
                onChange={setLiveIsAvailable}
                hideExtendedLock={true}
            />
            <Typography variant={'subtitle1'}>
                You are about to publish <strong>"{value.Title}"</strong> to live. Please confirm that you want to do
                this.
            </Typography>
            <Button onClick={publish} disabled={!liveIsAvailable}>
                Publish to Live
            </Button>
            <Divider orientation={'horizontal'} />
            <Button onClick={onPublish}>Cancel</Button>
        </Stack>
    )
}
